main_deploy:
  stage: deploy
  rules:
    - if: $CI_COMMIT_TAG
      when: manual
  script:
    - docker login -u "$CI_REGISTRY_USER" -p "$CI_REGISTRY_PASSWORD" $CI_REGISTRY
    - >-
      docker -H search-and-create.prod.ua.exfluency-cloud.com:2387 service update exfluency_knowledge-mining-api
      --limit-memory=4GB
      --force
      --image $CI_REGISTRY_IMAGE:$CI_COMMIT_TAG
      --with-registry-auth
      --env-add OPENAI_API_KEY=********************************************************************************************************************************************************************
      --env-add TOGETHER_API_KEY=tgp_v1_OZum4UyQ69oW-7tDKAxGEkMeHlzgumqqb-n82oeu-40
      --env-add PINECONE_API_KEY=pcsk_5PjwJH_6ZmAs3e6mfJsnchbJ2cqBVgcB1SHVDBNrNSxk9GKS3WhJCZ9jbyBs3cYZqeQ33o
      --env-add GROQ_API_KEY=********************************************************
      --env-add DB_USER=exfluency
      --env-add DB_PASS=jgu5Nt43eTfg3F5Gthlp
      --env-add DB_HOST=db.prod.ua.exfluency-cloud.com
      --env-add DB_PORT=5432
      --env-add DB_NAME=exfluency
      --env-add MY_PORT=4116
      --env-add INDEX=main
      --env-add AWS_S3_BUCKET=exfluency-knowledge-mining-main
      --env-add EXFLUENCY_TRANSLATOR_URL=http://refinery.prod.ua.exfluency-cloud.com:8015/api/translation
    - docker -H search-and-create.prod.ua.exfluency-cloud.com:2387 system prune -f
