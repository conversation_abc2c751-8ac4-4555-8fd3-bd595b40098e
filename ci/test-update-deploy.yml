test_deploy:
  stage: deploy
  rules:
    - if: $CI_COMMIT_TAG
    - if: $CI_COMMIT_BRANCH == "main"
      when: on_success
  script:
    - docker login -u "$CI_REGISTRY_USER" -p "$CI_REGISTRY_PASSWORD" $CI_REGISTRY
    - IMAGE_TAG=${CI_COMMIT_TAG:-$CI_COMMIT_SHORT_SHA}
    - >-
      docker -H test.exfluency.com:2387 service update exfluency_exfluency-knowledge-mining-api
      --limit-memory=4GB
      --force
      --image $CI_REGISTRY_IMAGE:$IMAGE_TAG
      --with-registry-auth
      --env-add OPENAI_API_KEY=********************************************************************************************************************************************************************
      --env-add TOGETHER_API_KEY=tgp_v1_OZum4UyQ69oW-7tDKAxGEkMeHlzgumqqb-n82oeu-40
      --env-add PINECONE_API_KEY=pcsk_6AR3dt_Eg8tax4kRrEpTwCV67Yq8sF85pXPNCi3o5ttiZSmGGEHpcoPnamTWet1G7YTHud
      --env-add GROQ_API_KEY=********************************************************
      --env-add DB_USER=exfluency
      --env-add DB_PASS=WdnjUYHftLi9YFpbMrS5jiozWspimH
      --env-add DB_HOST=postgres
      --env-add DB_PORT=5432
      --env-add DB_NAME=exfluency
      --env-add MY_PORT=4116
      --env-add INDEX=test
      --env-add AWS_SECRET_ACCESS_KEY=BIer3tdGII44+izf/nRvZdFzCbcIHGzOrd6QNRFc
      --env-add AWS_ACCESS_KEY_ID=********************
      --env-add AWS_S3_BUCKET=exfluency-knowledge-mining-test
      --env-add EXFLUENCY_TRANSLATOR_URL=http://exfluency-immediate-translation:8015/api/translation
      --env-add LOGSTASH_HOST=logstash
      --env-add LOGSTASH_PORT=5000
    - docker -H test.exfluency.com:2387 system prune -f
